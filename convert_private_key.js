const { Keypair } = require('@solana/web3.js');
const fs = require('fs');

// 🔑 在这里填写你的Phantom钱包导出的Base58格式私钥
const PRIVATE_KEY = "5ynF4o6m5CmU29YnKdkCX67Cq3rBAiik4GDLWSM1ZuJyxTsXgH6BpUCnnWZZenF21KJj95kuUR8mPcscy2HV1ZDw";

/**
 * 将Base58格式私钥转换为Solana JSON格式
 */
function convertPrivateKeyToJson() {
    try {
        console.log('🔑 开始转换Base58私钥...');

        // 验证私钥不为空
        if (!PRIVATE_KEY || PRIVATE_KEY.trim() === '') {
            throw new Error('请填写有效的Base58格式私钥');
        }

        // 使用@solana/web3.js的内置方法解码Base58私钥
        // Phantom导出的私钥是Base58格式，需要先解码
        const decoded = Buffer.from(PRIVATE_KEY, 'base58');
        const keypair = Keypair.fromSecretKey(decoded);

        // 获取完整的64字节密钥
        const secretKey = keypair.secretKey;

        // 转换为数组格式
        const keypairArray = Array.from(secretKey);

        // 写入keypair.json文件
        fs.writeFileSync('keypair.json', JSON.stringify(keypairArray, null, 0));

        console.log('✅ 转换成功！');
        console.log(`📍 公钥地址: ${keypair.publicKey.toString()}`);
        console.log(`📄 已生成文件: keypair.json`);
        console.log(`🔢 JSON格式: [${keypairArray.slice(0, 5).join(',')}...] (共${keypairArray.length}个数字)`);

    } catch (error) {
        console.error('❌ 转换失败:', error.message);
        console.log('💡 请确保私钥是从Phantom钱包导出的Base58格式字符串');
        console.log('💡 示例格式: 5ynF4o6m5CmU29YnKdkCX67Cq3rBAiik4GDLWSM1ZuJyxTsXgH6BpUCnnWZZenF21KJj95kuUR8mPcscy2HV1ZDw');
    }
}

// 直接运行转换
convertPrivateKeyToJson();
