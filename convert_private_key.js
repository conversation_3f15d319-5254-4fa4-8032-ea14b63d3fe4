const { Keypair } = require('@solana/web3.js');
const fs = require('fs');

// 🔑 在这里填写你要转换的64位私钥
const PRIVATE_KEY = "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456";

/**
 * 将64位私钥转换为Solana JSON格式
 */
function convertPrivateKeyToJson() {
    try {
        console.log('🔑 开始转换私钥...');
        
        // 移除可能的0x前缀
        const cleanHex = PRIVATE_KEY.replace(/^0x/, '');
        
        // 验证私钥长度
        if (cleanHex.length !== 64) {
            throw new Error('私钥必须是64位十六进制字符串');
        }
        
        // 将hex字符串转换为Uint8Array
        const privateKeyBytes = new Uint8Array(
            cleanHex.match(/.{1,2}/g).map(byte => parseInt(byte, 16))
        );
        
        // 创建Keypair对象
        const keypair = Keypair.fromSecretKey(privateKeyBytes);
        
        // 获取完整的64字节密钥（32字节私钥 + 32字节公钥）
        const secretKey = keypair.secretKey;
        
        // 转换为数组格式
        const keypairArray = Array.from(secretKey);
        
        // 写入keypair.json文件
        fs.writeFileSync('keypair.json', JSON.stringify(keypairArray, null, 0));
        
        console.log('✅ 转换成功！');
        console.log(`📍 公钥地址: ${keypair.publicKey.toString()}`);
        console.log(`📄 已生成文件: keypair.json`);
        console.log(`🔢 JSON格式: [${keypairArray.slice(0, 5).join(',')}...] (共${keypairArray.length}个数字)`);
        
    } catch (error) {
        console.error('❌ 转换失败:', error.message);
    }
}

// 直接运行转换
convertPrivateKeyToJson();
