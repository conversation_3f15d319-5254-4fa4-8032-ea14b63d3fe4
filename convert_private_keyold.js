const { generateKeypairJson, readKeypairInfo } = require('./generate_keypair_json');

// 🔑 在这里填写你的64位私钥
const PRIVATE_KEYS = {
    // 示例私钥（请替换为你的真实私钥）
    wallet1: "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456",
    wallet2: "b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456a1",
    wallet3: "c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456a1b2",
    
    // 添加更多钱包...
    // walletName: "your_64_char_private_key_here"
};

/**
 * 批量生成多个钱包的JSON文件
 */
function generateAllWallets() {
    console.log('🚀 开始批量生成钱包JSON文件...\n');
    
    Object.entries(PRIVATE_KEYS).forEach(([walletName, privateKey]) => {
        console.log(`📝 处理钱包: ${walletName}`);
        const outputPath = `${walletName}_keypair.json`;
        
        const result = generateKeypairJson(privateKey, outputPath);
        if (result.success) {
            console.log(`✅ ${walletName} 生成成功\n`);
        } else {
            console.log(`❌ ${walletName} 生成失败: ${result.error}\n`);
        }
    });
}

/**
 * 生成单个指定的钱包
 * @param {string} walletName - 钱包名称
 */
function generateSingleWallet(walletName) {
    if (!PRIVATE_KEYS[walletName]) {
        console.error(`❌ 找不到钱包: ${walletName}`);
        console.log('可用的钱包:', Object.keys(PRIVATE_KEYS).join(', '));
        return;
    }
    
    console.log(`🔑 生成钱包: ${walletName}`);
    const privateKey = PRIVATE_KEYS[walletName];
    const outputPath = `${walletName}_keypair.json`;
    
    const result = generateKeypairJson(privateKey, outputPath);
    if (result.success) {
        console.log(`✅ ${walletName} 生成成功`);
    } else {
        console.log(`❌ ${walletName} 生成失败: ${result.error}`);
    }
}

/**
 * 快速生成到默认的keypair.json文件
 * @param {string} walletName - 钱包名称
 */
function generateToDefault(walletName) {
    if (!PRIVATE_KEYS[walletName]) {
        console.error(`❌ 找不到钱包: ${walletName}`);
        console.log('可用的钱包:', Object.keys(PRIVATE_KEYS).join(', '));
        return;
    }
    
    console.log(`🔑 将 ${walletName} 生成为 keypair.json`);
    const privateKey = PRIVATE_KEYS[walletName];
    
    const result = generateKeypairJson(privateKey, 'keypair.json');
    if (result.success) {
        console.log(`✅ keypair.json 生成成功`);
    } else {
        console.log(`❌ 生成失败: ${result.error}`);
    }
}

// 命令行使用
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log(`
🔑 私钥转换工具

使用方法:
  node convert_private_key.js --all                    # 生成所有钱包
  node convert_private_key.js --single <钱包名>        # 生成指定钱包
  node convert_private_key.js --default <钱包名>       # 生成到 keypair.json
  node convert_private_key.js --list                   # 列出所有可用钱包

可用钱包: ${Object.keys(PRIVATE_KEYS).join(', ')}
        `);
        process.exit(0);
    }
    
    const command = args[0];
    
    switch (command) {
        case '--all':
            generateAllWallets();
            break;
            
        case '--single':
            if (args[1]) {
                generateSingleWallet(args[1]);
            } else {
                console.error('❌ 请指定钱包名称');
            }
            break;
            
        case '--default':
            if (args[1]) {
                generateToDefault(args[1]);
            } else {
                console.error('❌ 请指定钱包名称');
            }
            break;
            
        case '--list':
            console.log('📋 可用钱包:');
            Object.keys(PRIVATE_KEYS).forEach(name => {
                console.log(`  - ${name}`);
            });
            break;
            
        default:
            console.error('❌ 未知命令:', command);
            break;
    }
}

module.exports = {
    generateAllWallets,
    generateSingleWallet,
    generateToDefault,
    PRIVATE_KEYS
};
