const { Keypair } = require('@solana/web3.js');
const fs = require('fs');

/**
 * 将64位私钥字符串转换为Solana JSON格式
 * @param {string} privateKeyHex - 64位私钥字符串（hex格式）
 * @param {string} outputPath - 输出文件路径，默认为 'keypair.json'
 */
function generateKeypairJson(privateKeyHex, outputPath = 'keypair.json') {
    try {
        // 移除可能的0x前缀
        const cleanHex = privateKeyHex.replace(/^0x/, '');
        
        // 验证私钥长度
        if (cleanHex.length !== 64) {
            throw new Error('私钥必须是64位十六进制字符串');
        }
        
        // 将hex字符串转换为Uint8Array
        const privateKeyBytes = new Uint8Array(
            cleanHex.match(/.{1,2}/g).map(byte => parseInt(byte, 16))
        );
        
        // 创建Keypair对象
        const keypair = Keypair.fromSecretKey(privateKeyBytes);
        
        // 获取完整的64字节密钥（32字节私钥 + 32字节公钥）
        const secretKey = keypair.secretKey;
        
        // 转换为数组格式
        const keypairArray = Array.from(secretKey);
        
        // 写入JSON文件
        fs.writeFileSync(outputPath, JSON.stringify(keypairArray, null, 0));
        
        console.log(`✅ 成功生成keypair文件: ${outputPath}`);
        console.log(`📍 公钥地址: ${keypair.publicKey.toString()}`);
        console.log(`📄 JSON格式预览: [${keypairArray.slice(0, 5).join(',')}...] (共${keypairArray.length}个数字)`);
        
        return {
            success: true,
            publicKey: keypair.publicKey.toString(),
            filePath: outputPath,
            keypairArray: keypairArray
        };
        
    } catch (error) {
        console.error('❌ 生成keypair失败:', error.message);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * 从现有的JSON文件读取并显示公钥信息
 * @param {string} jsonPath - JSON文件路径
 */
function readKeypairInfo(jsonPath = 'keypair.json') {
    try {
        const data = fs.readFileSync(jsonPath, 'utf8');
        const keypairArray = JSON.parse(data);
        
        const secretKey = new Uint8Array(keypairArray);
        const keypair = Keypair.fromSecretKey(secretKey);
        
        console.log(`📖 读取keypair文件: ${jsonPath}`);
        console.log(`📍 公钥地址: ${keypair.publicKey.toString()}`);
        console.log(`🔑 私钥长度: ${keypairArray.length} 字节`);
        
        return {
            success: true,
            publicKey: keypair.publicKey.toString(),
            keypairArray: keypairArray
        };
        
    } catch (error) {
        console.error('❌ 读取keypair失败:', error.message);
        return {
            success: false,
            error: error.message
        };
    }
}

// 使用示例和命令行接口
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log(`
🔑 Solana Keypair JSON 生成器

使用方法:
  node generate_keypair_json.js <64位私钥> [输出文件名]
  node generate_keypair_json.js --read [JSON文件路径]

示例:
  # 生成新的keypair.json
  node generate_keypair_json.js a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456

  # 生成到指定文件
  node generate_keypair_json.js a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456 my_wallet.json

  # 读取现有JSON文件信息
  node generate_keypair_json.js --read keypair.json

注意: 请确保你的64位私钥是有效的十六进制字符串
        `);
        process.exit(0);
    }
    
    if (args[0] === '--read') {
        const jsonPath = args[1] || 'keypair.json';
        readKeypairInfo(jsonPath);
    } else {
        const privateKeyHex = args[0];
        const outputPath = args[1] || 'keypair.json';
        generateKeypairJson(privateKeyHex, outputPath);
    }
}

// 导出函数供其他模块使用
module.exports = {
    generateKeypairJson,
    readKeypairInfo
};
